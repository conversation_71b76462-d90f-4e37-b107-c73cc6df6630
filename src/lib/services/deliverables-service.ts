import { db } from "@/drizzle/db"
import {
  cycles,
  deliverableDelegations,
  deliverableDrafts,
  deliverableRevisions,
  deliverableTemplates,
  deliverables,
} from "@/drizzle/schema"
import { addDays } from "date-fns"
import { and, eq, sql } from "drizzle-orm"

export class DeliverablesService {
  /**
   * Auto-schedule deliverables when a placement is confirmed
   * @param studentId - The student's user ID
   * @param placementStartDate - The start date of the placement
   * @param placementType - 'organization' | 'faculty'
   * @param supervisorId - The faculty or organization user ID
   */
  static async scheduleDeliverablesForStudent(
    studentId: string,
    placementStartDate: Date,
    placementType: "organization" | "faculty",
    supervisorId: string
  ) {
    // Get the current active cycle
    const currentCycle = await db.query.cycles.findFirst({
      where: eq(cycles.isCurrent, true),
    })

    if (!currentCycle) {
      throw new Error("No active cycle found")
    }

    // Get all active deliverable templates for this cycle
    const templates = await db.query.deliverableTemplates.findMany({
      where: and(
        eq(deliverableTemplates.cycleId, currentCycle.id),
        eq(deliverableTemplates.isActive, true)
      ),
      orderBy: (templates, { asc }) => [asc(templates.displayOrder)],
    })

    // Create deliverables for each template
    const createdDeliverables = []
    for (const template of templates) {
      const dueDate = addDays(placementStartDate, template.daysFromStart)

      const [deliverable] = await db
        .insert(deliverables)
        .values({
          studentId,
          facultyId: placementType === "faculty" ? supervisorId : null,
          organizationId:
            placementType === "organization" ? supervisorId : null,
          cycleId: currentCycle.id,
          templateId: template.id,
          type: template.type,
          title: template.name,
          description: template.description,
          dueDate,
          status: "pending",
        })
        .returning()

      createdDeliverables.push(deliverable)
    }

    return createdDeliverables
  }

  /**
   * Submit a deliverable with content and attachments
   */
  static async submitDeliverable(
    deliverableId: string,
    userId: string,
    content: string,
    attachments: Array<{
      id: string
      name: string
      url: string
      size: number
      mimeType: string
      uploadedAt: string
    }> = []
  ) {
    // Get the deliverable
    const deliverable = await db.query.deliverables.findFirst({
      where: eq(deliverables.id, deliverableId),
      with: {
        revisions: {
          orderBy: (revisions, { desc }) => [desc(revisions.version)],
          limit: 1,
        },
      },
    })

    if (!deliverable) {
      throw new Error("Deliverable not found")
    }

    if (deliverable.studentId !== userId) {
      throw new Error("Unauthorized to submit this deliverable")
    }

    // Calculate the next version number
    const nextVersion = deliverable.revisions[0]?.version
      ? deliverable.revisions[0].version + 1
      : 1

    // Create a new revision
    const [revision] = await db
      .insert(deliverableRevisions)
      .values({
        deliverableId,
        version: nextVersion,
        content,
        attachments: attachments.map((a) => ({
          ...a,
          uploadedAt: new Date().toISOString(),
        })),
        submittedBy: userId,
        status: "submitted",
      })
      .returning()

    // Update the deliverable
    await db
      .update(deliverables)
      .set({
        currentRevisionId: revision.id,
        status: "submitted",
        submittedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(deliverables.id, deliverableId))

    // Clear any existing draft
    await db
      .delete(deliverableDrafts)
      .where(
        and(
          eq(deliverableDrafts.deliverableId, deliverableId),
          eq(deliverableDrafts.userId, userId)
        )
      )

    return revision
  }

  /**
   * Save a draft of a deliverable (auto-save functionality)
   * Now includes conflict detection for concurrent edits
   */
  static async saveDraft(
    deliverableId: string,
    userId: string,
    content: string,
    attachments: Array<{
      id: string
      name: string
      url: string
      size: number
      mimeType: string
      uploadedAt?: string
    }> = [],
    clientVersion: number = 1
  ) {
    const { ConflictResolutionService } = await import(
      "./conflict-resolution-service.js"
    )

    // Use conflict-aware save method
    const result =
      await ConflictResolutionService.saveDraftWithConflictDetection(
        deliverableId,
        userId,
        content,
        attachments,
        clientVersion
      )

    // Return legacy format for backwards compatibility
    if (result.conflict) {
      throw new Error(
        `Conflict detected: ${result.conflict.conflictType}. ` +
          `Your version: ${result.conflict.clientVersion}, ` +
          `Server version: ${result.conflict.serverVersion}. ` +
          `Please refresh and resolve conflicts.`
      )
    }

    return result.draft
  }

  /**
   * Legacy save draft method (fallback)
   */
  static async saveDraftLegacy(
    deliverableId: string,
    userId: string,
    content: string,
    attachments: Array<{
      id: string
      name: string
      url: string
      size: number
      mimeType: string
      uploadedAt?: string
    }> = []
  ) {
    // Upsert the draft
    const [draft] = await db
      .insert(deliverableDrafts)
      .values({
        deliverableId,
        userId,
        content,
        attachments: attachments.map((att) => ({
          ...att,
          uploadedAt: att.uploadedAt || new Date().toISOString(),
        })),
        lastSavedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: [deliverableDrafts.deliverableId, deliverableDrafts.userId],
        set: {
          content,
          attachments: attachments.map((att) => ({
            ...att,
            uploadedAt: att.uploadedAt || new Date().toISOString(),
          })),
          lastSavedAt: new Date(),
        },
      })
      .returning()

    return draft
  }

  /**
   * Start an edit session for conflict prevention
   */
  static async startEditSession(deliverableId: string, userId: string) {
    const { ConflictResolutionService } = await import(
      "./conflict-resolution-service.js"
    )
    return ConflictResolutionService.startEditSession(deliverableId, userId)
  }

  /**
   * End an edit session and release locks
   */
  static async endEditSession(deliverableId: string, sessionId: string) {
    const { ConflictResolutionService } = await import(
      "./conflict-resolution-service.js"
    )
    return ConflictResolutionService.endEditSession(deliverableId, sessionId)
  }

  /**
   * Check for conflicts before saving
   */
  static async checkForConflicts(
    deliverableId: string,
    clientVersion: number,
    userId: string
  ) {
    const { ConflictResolutionService } = await import(
      "./conflict-resolution-service.js"
    )
    return ConflictResolutionService.detectConflict(
      deliverableId,
      clientVersion,
      userId
    )
  }

  /**
   * Resolve conflicts using specified strategy
   */
  static async resolveConflict(
    deliverableId: string,
    userId: string,
    strategy: "user_merge" | "server_wins" | "client_wins" | "manual",
    clientContent: string,
    clientAttachments: Array<{
      id: string
      name: string
      url: string
      size: number
      mimeType: string
      uploadedAt?: string
    }> = [],
    clientVersion: number
  ) {
    const { ConflictResolutionService } = await import(
      "./conflict-resolution-service.js"
    )
    return ConflictResolutionService.resolveConflict(
      deliverableId,
      userId,
      strategy,
      clientContent,
      clientAttachments,
      clientVersion
    )
  }

  /**
   * Get a draft for a deliverable
   */
  static async getDraft(deliverableId: string, userId: string) {
    return db.query.deliverableDrafts.findFirst({
      where: and(
        eq(deliverableDrafts.deliverableId, deliverableId),
        eq(deliverableDrafts.userId, userId)
      ),
    })
  }

  /**
   * Faculty/Organization reviews a deliverable submission
   */
  static async reviewDeliverable(
    deliverableId: string,
    reviewerId: string,
    status: "approved" | "needs_revision",
    feedback: string
  ) {
    // Get the deliverable with current revision
    const deliverable = await db.query.deliverables.findFirst({
      where: eq(deliverables.id, deliverableId),
    })
    
    // Get active delegations separately if needed
    const activeDelegations = await db.query.deliverableDelegations.findMany({
      where: and(
        eq(deliverableDelegations.deliverableId, deliverableId),
        eq(deliverableDelegations.isActive, true)
      ),
    })

    if (!deliverable || !deliverable.currentRevisionId) {
      throw new Error("Deliverable or submission not found")
    }

    // Check authorization (faculty, organization, or delegated mentor)
    const isAuthorized =
      deliverable.facultyId === reviewerId ||
      deliverable.organizationId === reviewerId ||
      activeDelegations.some(d => d.delegatedTo === reviewerId)

    if (!isAuthorized) {
      throw new Error("Unauthorized to review this deliverable")
    }

    // Update the current revision with feedback
    await db
      .update(deliverableRevisions)
      .set({
        status,
        feedback,
        feedbackBy: reviewerId,
        feedbackAt: new Date(),
      })
      .where(eq(deliverableRevisions.id, deliverable.currentRevisionId))

    // Update the deliverable status
    await db
      .update(deliverables)
      .set({
        status,
        reviewedBy: reviewerId,
        reviewedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(deliverables.id, deliverableId))

    return { status, feedback }
  }

  /**
   * Delegate review authority to a mentor
   */
  static async delegateReview(
    deliverableId: string,
    delegatorId: string,
    delegateId: string,
    reason?: string
  ) {
    // Verify the delegator has authority
    const deliverable = await db.query.deliverables.findFirst({
      where: and(
        eq(deliverables.id, deliverableId),
        sql`(${deliverables.facultyId} = ${delegatorId} OR ${deliverables.organizationId} = ${delegatorId})`
      ),
    })

    if (!deliverable) {
      throw new Error("Unauthorized to delegate this deliverable")
    }

    // Deactivate any existing delegations
    await db
      .update(deliverableDelegations)
      .set({ isActive: false })
      .where(eq(deliverableDelegations.deliverableId, deliverableId))

    // Create new delegation
    const [delegation] = await db
      .insert(deliverableDelegations)
      .values({
        deliverableId,
        delegatedFrom: delegatorId,
        delegatedTo: delegateId,
        reason,
        isActive: true,
      })
      .returning()

    return delegation
  }

  /**
   * Get all deliverables for a student
   */
  static async getStudentDeliverables(studentId: string) {
    const deliverablesList = await db.query.deliverables.findMany({
      where: eq(deliverables.studentId, studentId),
      with: {
        template: true,
        revisions: {
          orderBy: (revisions, { desc }) => [desc(revisions.version)],
        },
        faculty: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: (deliverables, { asc }) => [asc(deliverables.dueDate)],
    })
    
    // Get delegations separately for each deliverable
    const deliverablesWithDelegations = await Promise.all(
      deliverablesList.map(async (deliverable) => {
        const delegations = await db.query.deliverableDelegations.findMany({
          where: and(
            eq(deliverableDelegations.deliverableId, deliverable.id),
            eq(deliverableDelegations.isActive, true)
          ),
          with: {
            delegatedToUser: {
              columns: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        })
        
        return {
          ...deliverable,
          delegations,
        }
      })
    )
    
    return deliverablesWithDelegations
  }

  /**
   * Get deliverables for review (faculty/organization/delegated mentor)
   */
  static async getDeliverablesForReview(reviewerId: string) {
    return db.query.deliverables.findMany({
      where: sql`
        (${deliverables.facultyId} = ${reviewerId} 
        OR ${deliverables.organizationId} = ${reviewerId}
        OR EXISTS (
          SELECT 1 FROM ${deliverableDelegations}
          WHERE ${deliverableDelegations.deliverableId} = ${deliverables.id}
          AND ${deliverableDelegations.delegatedTo} = ${reviewerId}
          AND ${deliverableDelegations.isActive} = true
        ))
        AND ${deliverables.status} IN ('submitted', 'under_review')
      `,
      with: {
        student: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        template: true,
        revisions: {
          orderBy: (revisions, { desc }) => [desc(revisions.version)],
        },
      },
      orderBy: (deliverables, { asc }) => [asc(deliverables.dueDate)],
    })
  }

  /**
   * Get revision history for a deliverable
   */
  static async getRevisionHistory(deliverableId: string, userId: string) {
    // Check if user has access to this deliverable
    const deliverable = await db.query.deliverables.findFirst({
      where: and(
        eq(deliverables.id, deliverableId),
        sql`
          (${deliverables.studentId} = ${userId}
          OR ${deliverables.facultyId} = ${userId}
          OR ${deliverables.organizationId} = ${userId}
          OR EXISTS (
            SELECT 1 FROM ${deliverableDelegations}
            WHERE ${deliverableDelegations.deliverableId} = ${deliverables.id}
            AND ${deliverableDelegations.delegatedTo} = ${userId}
            AND ${deliverableDelegations.isActive} = true
          ))
        `
      ),
    })

    if (!deliverable) {
      throw new Error("Unauthorized to view this deliverable")
    }

    return db.query.deliverableRevisions.findMany({
      where: eq(deliverableRevisions.deliverableId, deliverableId),
      with: {
        submittedByUser: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
        feedbackByUser: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: (revisions, { desc }) => [desc(revisions.version)],
    })
  }
}
