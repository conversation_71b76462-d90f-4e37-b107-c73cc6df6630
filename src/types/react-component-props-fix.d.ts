// Fix for React 19 ComponentProps not including children
// This patches React.ComponentProps to work correctly with HTML elements

import "react"

declare module "react" {
  // Override ComponentProps to ensure it includes children for HTML elements
  type ComponentProps<T extends keyof JSX.IntrinsicElements | React.JSXElementConstructor<any>> =
    T extends React.JSXElementConstructor<infer P>
      ? P
      : T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T]
      : {}

  // Note: HTMLAttributes is already extended in react19-override.d.ts
  // Removed duplicate interface declaration to avoid conflicts
}

export {}