// Type overrides to fix TS71007 errors with Next.js Server Actions
// This tells TypeScript that these are regular props, not Server Actions

// Removed global React overrides - using component-level typing instead

// Extend component prop types to include onOpenChange
declare global {
  namespace JSX {
    interface IntrinsicElements {
      dialog: React.DetailedHTMLProps<
        React.DialogHTMLAttributes<HTMLDialogElement>,
        HTMLDialogElement
      >
    }
  }
}

export {}
