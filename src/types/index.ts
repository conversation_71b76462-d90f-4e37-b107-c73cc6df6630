// Re-export everything except the conflicting types
export * from "./navigation"
export * from "./ui-components"

// Export i18n types with specific names to avoid conflicts
export type {
  LocaleType as I18nLocaleType,
  FormatStyleType as I18nFormatStyleType,
} from "./i18n"
export type { DictionaryType } from "@/lib/get-dictionary"

// Export settings types with specific names to avoid conflicts
export type {
  UserType,
  ThemeType,
  SettingsType,
  DirectionType,
  IconProps,
  IconType,
  DynamicIconNameType,
  FileType,
} from "./settings"
export type { LocaleType, FormatStyleType } from "./settings"
