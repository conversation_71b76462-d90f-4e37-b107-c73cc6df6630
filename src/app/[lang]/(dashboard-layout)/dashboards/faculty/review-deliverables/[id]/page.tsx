import { notFound } from "next/navigation"
import { db } from "@/drizzle/db"
import {
  deliverableDelegations,
  deliverableRevisions,
  deliverables,
} from "@/drizzle/schema"
import { and, eq, or, sql } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { Metadata } from "next"

import { authOptions } from "@/configs/next-auth"

import { DeliverableReviewForm } from "./_components/deliverable-review-form"

export const metadata: Metadata = {
  title: "Review Deliverable | Faculty",
  description: "Review and provide feedback on student deliverable",
}

interface PageProps {
  params: Promise<{
    id: string
  }>
}

export default async function FacultyReviewDeliverablePage({
  params,
}: PageProps) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id || session.user.role !== "faculty") {
    notFound()
  }

  const { id } = await params

  // Get the deliverable - check if faculty has access (direct or delegated)
  const deliverable = await db.query.deliverables.findFirst({
    where: and(
      eq(deliverables.id, id),
      sql`
        (${deliverables.facultyId} = ${session.user.id}
        OR EXISTS (
          SELECT 1 FROM ${deliverableDelegations}
          WHERE ${deliverableDelegations.deliverableId} = ${deliverables.id}
          AND ${deliverableDelegations.delegatedTo} = ${session.user.id}
          AND ${deliverableDelegations.isActive} = true
        ))
      `
    ),
    with: {
      template: true,
      student: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      revisions: {
        orderBy: (revisions, { desc }) => [desc(revisions.version)],
        with: {
          submittedByUser: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          feedbackByUser: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      },
      currentRevision: true,
      delegations: {
        where: eq(deliverableDelegations.isActive, true),
        with: {
          delegatedFromUser: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          delegatedToUser: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      },
    },
  })

  if (!deliverable) {
    notFound()
  }

  return (
    <div className="mx-auto max-w-4xl space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Review Deliverable
        </h1>
        <p className="text-muted-foreground">
          Student: {deliverable.student.firstName}{" "}
          {deliverable.student.lastName}
        </p>
      </div>

      <DeliverableReviewForm
        deliverable={deliverable as any}
        reviewerId={session.user.id}
      />
    </div>
  )
}
