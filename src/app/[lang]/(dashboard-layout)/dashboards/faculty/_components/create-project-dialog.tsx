"use client"

import { useEffect, useState } from "react"
import { format } from "date-fns"
import { toast } from "sonner"
import { CalendarIcon, X } from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Textarea } from "@/components/ui/textarea"

interface Project {
  id: string
  title: string // Changed from 'name' to match ProjectsList
  description: string
  status: "active" | "completed" | "paused"
  startDate: string
  endDate?: string
  students: Array<{
    id: string
    name?: string
    email?: string
  }>
  tasks?: Array<{ // Made optional to match ProjectsList
    title: string
    description?: string
    status: "todo" | "in_progress" | "completed"
    assignedTo?: string
    dueDate?: string
    completedAt?: string
  }>
  supervisors?: Array<{ // Added to match ProjectsList
    id: string
    name?: string
    email?: string
    isPrimary: boolean
  }>
  createdAt: string
  updatedAt: string
}

interface CreateProjectDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProjectCreated: (project: Project) => void
}

interface FacultyMember {
  id: string
  name: string
  email: string
}

export function CreateProjectDialog({
  open,
  onOpenChange,
  onProjectCreated,
}: CreateProjectDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [startDate, setStartDate] = useState<Date | undefined>(new Date())
  const [endDate, setEndDate] = useState<Date | undefined>()
  const [facultySearch, setFacultySearch] = useState("")
  const [showFacultySearch, setShowFacultySearch] = useState(false)
  const [availableFaculty, setAvailableFaculty] = useState<FacultyMember[]>([])
  const [selectedCoSupervisors, setSelectedCoSupervisors] = useState<
    FacultyMember[]
  >([])
  const [loadingFaculty, setLoadingFaculty] = useState(false)

  // Search for faculty members
  useEffect(() => {
    const searchFaculty = async () => {
      if (facultySearch.length < 2) {
        setAvailableFaculty([])
        return
      }

      setLoadingFaculty(true)
      try {
        const response = await fetch(
          `/api/users/search?role=faculty&q=${facultySearch}`
        )
        if (response.ok) {
          const data = await response.json()
          // Filter out already selected faculty
          const filtered = data.filter(
            (f: FacultyMember) =>
              !selectedCoSupervisors.some((s) => s.id === f.id)
          )
          setAvailableFaculty(filtered)
        }
      } catch (error) {
        console.error("Failed to search faculty:", error)
      } finally {
        setLoadingFaculty(false)
      }
    }

    const debounce = setTimeout(searchFaculty, 300)
    return () => clearTimeout(debounce)
  }, [facultySearch, selectedCoSupervisors])

  const addCoSupervisor = (faculty: FacultyMember) => {
    setSelectedCoSupervisors([...selectedCoSupervisors, faculty])
    setFacultySearch("")
    setShowFacultySearch(false)
  }

  const removeCoSupervisor = (facultyId: string) => {
    setSelectedCoSupervisors(
      selectedCoSupervisors.filter((f) => f.id !== facultyId)
    )
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    const formData = new FormData(e.currentTarget)
    const data = {
      title: formData.get("name") as string,
      description: formData.get("description") as string,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString(),
      coSupervisorIds: selectedCoSupervisors.map((f) => f.id),
    }

    // Validate dates
    if (!startDate) {
      toast.error("Please select a start date")
      setIsSubmitting(false)
      return
    }

    if (endDate && endDate <= startDate) {
      toast.error("End date must be after start date")
      setIsSubmitting(false)
      return
    }

    try {
      const response = await fetch("/api/faculty/projects", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to create project")
      }

      const newProject = await response.json()
      onProjectCreated(newProject)
      toast.success("Project created successfully")

      // Reset form
      ;(e.target as HTMLFormElement).reset()
      setStartDate(new Date())
      setEndDate(undefined)
    } catch (error) {
      console.error("Failed to create project:", error)
      toast.error(
        error instanceof Error ? error.message : "Failed to create project"
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>Create Project</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Create a new research project. You can add students and tasks
              after creating the project.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Project Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="e.g., Community Legal Aid Research"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Describe the project objectives and scope..."
                className="min-h-[100px]"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label>Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label>End Date (Optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    disabled={(date) => date < (startDate || new Date())}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label>Co-Supervisors (Optional)</Label>
              <div className="space-y-2">
                {selectedCoSupervisors.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {selectedCoSupervisors.map((faculty) => (
                      <Badge key={faculty.id} variant="secondary">
                        {faculty.name}
                        <button
                          type="button"
                          onClick={() => removeCoSupervisor(faculty.id)}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
                <Popover
                  open={showFacultySearch}
                  onOpenChange={setShowFacultySearch}
                >
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full justify-start"
                    >
                      Add co-supervisor...
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search faculty..."
                        value={facultySearch}
                        onValueChange={setFacultySearch}
                      />
                      {loadingFaculty ? (
                        <div className="p-2 text-sm text-muted-foreground">
                          Searching...
                        </div>
                      ) : availableFaculty.length === 0 ? (
                        <CommandEmpty>
                          {facultySearch.length < 2
                            ? "Type at least 2 characters to search"
                            : "No faculty found"}
                        </CommandEmpty>
                      ) : (
                        <CommandGroup>
                          {availableFaculty.map((faculty) => (
                            <CommandItem
                              key={faculty.id}
                              onSelect={() => addCoSupervisor(faculty)}
                            >
                              <div>
                                <div className="font-medium">
                                  {faculty.name}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {faculty.email}
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      )}
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Project"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
