"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Cell, LabelList, XAxis } from "recharts"

import type { CSSProperties, ComponentProps } from "react"
import type { ChurnRateType } from "../types"
import type { ChartTooltipPayloadItem } from "@/components/ui/chart.types"

import { camelCaseToTitleCase, formatPercent } from "@/lib/utils"

import { useIsRtl } from "@/hooks/use-is-rtl"
import { useRadius } from "@/hooks/use-radius"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

function ModifiedChartTooltipContent(
  props: ComponentProps<typeof ChartTooltipContent>
) {
  if (!props.payload || props.payload.length === 0) return null

  return (
    <ChartTooltipContent
      {...props}
      payload={props.payload.map((item: any) => ({
        ...item,
        name: camelCaseToTitleCase(String(item.name || item.dataKey)),
        value: item.value?.toLocaleString(),
      }))}
    />
  )
}

export function ChurnRateChart({ data }: { data: ChurnRateType["months"] }) {
  const isRtl = useIsRtl()
  const radius = useRadius()

  return (
    <ChartContainer config={{}} className="aspect-auto grow w-full">
      <BarChart accessibilityLayer data={data} margin={{ top: 20, bottom: 0 }}>
        <CartesianGrid vertical={false} />
        <ChartTooltip
          cursor={false}
          content={<ModifiedChartTooltipContent />}
        />
        <XAxis
          reversed={isRtl}
          dataKey="month"
          tickLine={false}
          axisLine={false}
          tickMargin={10}
          tickFormatter={(value) => value.slice(0, 3)}
        />
        <Bar
          dataKey="totalCustomers"
          stackId="a"
          fill="hsl(var(--chart-2))"
          radius={[0, 0, radius, radius]}
        />

        <Bar
          dataKey="lostCustomers"
          stackId="a"
          fill="hsl(var(--chart-1))"
          radius={[radius, radius, 0, 0]}
        >
          <LabelList
            position="top"
            dataKey="churnRate"
            formatter={(value: number) => formatPercent(value)}
            fontWeight={700}
          />
          {data.map((item) => (
            <Cell key={item.month} fill="hsl(var(--chart-1))" />
          ))}
        </Bar>
      </BarChart>
    </ChartContainer>
  )
}
