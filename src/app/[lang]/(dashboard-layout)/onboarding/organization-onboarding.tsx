"use client"

import { useEffe<PERSON>, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResol<PERSON> } from "@hookform/resolvers/zod"
import { AnimatePresence, motion } from "framer-motion"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { ChevronLeft, ChevronRight, Loader2, Plus, X } from "lucide-react"

import type { DictionaryType } from "@/lib/get-dictionary"

import { toast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { InputSpin } from "@/components/ui/input-spin"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface OrganizationOnboardingProps {
  dictionary: DictionaryType
}

const orgOnboardingSchema = z.discriminatedUnion("hasOnboardedStudents", [
  z.object({
    hasOnboardedStudents: z.literal(true),
    studentNames: z
      .array(z.string().min(1, "Student/intern name is required"))
      .min(1, "At least one name is required"),
    areaOfLawId: z.string().min(1, "Primary area of law is required"),
    workArrangements: z
      .array(z.enum(["remote", "hybrid", "in_person"]))
      .min(1, "At least one work arrangement is required")
  }),
  z.object({
    hasOnboardedStudents: z.literal(false),
    projectDescription: z
      .string()
      .min(10, "Project description must be at least 10 characters")
      .max(500),
    studentsRequested: z.number().min(1).max(20),
    positionType: z.enum([
      "internship",
      "parttime",
      "fulltime",
      "contract",
      "volunteer",
    ]),
    minimumYear: z.enum([
      "any",
      "first",
      "second",
      "third",
      "fourth",
      "graduate",
    ]),
    compensationType: z.enum(["paid", "unpaid", "credit", "stipend"]),
    preferredMajors: z.array(z.string().min(1)).optional(),
    requiredSkills: z.array(z.string().min(1)).optional(),
    areaOfLawId: z.string().min(1, "Primary area of law is required"),
    workArrangements: z
      .array(z.enum(["remote", "hybrid", "in_person"]))
      .min(1, "At least one work arrangement is required"),
    maxStudents: z.number().min(1).max(4).default(1),
  }),
])

type OrgOnboardingData = z.infer<typeof orgOnboardingSchema>

export function OrganizationOnboarding({
  dictionary: _dictionary,
}: OrganizationOnboardingProps) {
  const { data: session, update } = useSession()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [hasOnboardedStudents, setHasOnboardedStudents] = useState<
    boolean | null
  >(null)
  const [studentNames, setStudentNames] = useState<string[]>([])
  const [preferredMajors, setPreferredMajors] = useState<string[]>([])
  const [requiredSkills, setRequiredSkills] = useState<string[]>([])
  const [newMajor, setNewMajor] = useState("")
  const [newSkill, setNewSkill] = useState("")
  const [areasOfLaw, setAreasOfLaw] = useState<
    Array<{ id: string; name: string; active: boolean }>
  >([])

  const form = useForm<OrgOnboardingData>({
    resolver: zodResolver(orgOnboardingSchema),
    defaultValues: {
      hasOnboardedStudents: false,
      studentsRequested: 1,
      minimumYear: "any",
      positionType: "internship",
      compensationType: "paid",
      workArrangements: ["in_person"],
      maxStudents: 1,
    },
  })

  useEffect(() => {
    // Fetch areas of law
    const fetchAreasOfLaw = async () => {
      try {
        const response = await fetch("/api/student/application/areas-of-law")
        if (response.ok) {
          const data = await response.json()
          setAreasOfLaw(data)
        }
      } catch (error) {
        console.error("Failed to fetch areas of law:", error)
      }
    }
    fetchAreasOfLaw()
  }, [])

  const handleInitialChoice = (value: boolean) => {
    setHasOnboardedStudents(value)
    form.setValue("hasOnboardedStudents", value)
    setCurrentStep(1)
  }

  const addStudentName = () => {
    setStudentNames([...studentNames, ""])
  }

  const updateStudentName = (index: number, value: string) => {
    const updated = [...studentNames]
    updated[index] = value
    setStudentNames(updated)
    form.setValue(
      "studentNames",
      updated.filter((name) => name.trim() !== "")
    )
  }

  const removeStudentName = (index: number) => {
    const updated = studentNames.filter((_, i) => i !== index)
    setStudentNames(updated)
    form.setValue(
      "studentNames",
      updated.filter((name) => name.trim() !== "")
    )
  }

  const addMajor = () => {
    if (newMajor.trim()) {
      const updated = [...preferredMajors, newMajor.trim()]
      setPreferredMajors(updated)
      form.setValue("preferredMajors", updated)
      setNewMajor("")
    }
  }

  const removeMajor = (index: number) => {
    const updated = preferredMajors.filter((_, i) => i !== index)
    setPreferredMajors(updated)
    form.setValue("preferredMajors", updated)
  }

  const addSkill = () => {
    if (newSkill.trim()) {
      const updated = [...requiredSkills, newSkill.trim()]
      setRequiredSkills(updated)
      form.setValue("requiredSkills", updated)
      setNewSkill("")
    }
  }

  const removeSkill = (index: number) => {
    const updated = requiredSkills.filter((_, i) => i !== index)
    setRequiredSkills(updated)
    form.setValue("requiredSkills", updated)
  }

  const onSubmit = async (data: OrgOnboardingData) => {
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/onboarding/organization", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          userId: session?.user?.id,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to complete onboarding")
      }

      // Update session to reflect onboarding completion
      await update()

      toast({
        title: "Welcome to Shadowland!",
        description: "Your organization profile has been set up successfully.",
      })

      router.push("/")
    } catch (_error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to complete onboarding. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (currentStep === 0) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">Organization Onboarding</CardTitle>
          <CardDescription>
            Welcome to the Shadowland talent portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">
              Have you already onboarded students or interns for current
              projects?
            </h3>
            <RadioGroup
              onValueChange={(value) => handleInitialChoice(value === "yes")}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="yes" />
                <Label htmlFor="yes">
                  Yes, we have already onboarded talent
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="no" />
                <Label htmlFor="no">No, we need to find talent</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {hasOnboardedStudents
            ? "List Your Current Team"
            : "Create Talent Request"}
        </CardTitle>
        <CardDescription>
          {hasOnboardedStudents
            ? "Please provide the names of students/interns you've already onboarded"
            : "Tell us about your projects and talent requirements"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <AnimatePresence mode="wait">
              {hasOnboardedStudents ? (
                <motion.div
                  key="student-list"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-4"
                >
                  <div>
                    <Label>Student/Intern Names</Label>
                    <div className="space-y-2 mt-2">
                      {studentNames.length === 0 && (
                        <p className="text-sm text-muted-foreground">
                          Add at least one name
                        </p>
                      )}
                      {studentNames.map((name, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={name}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            updateStudentName(index, e.target.value)
                            }
                            placeholder="Enter full name"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeStudentName(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addStudentName}
                        className="mt-2"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Person
                      </Button>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="areaOfLawId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Area of Law</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select area of law" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {areasOfLaw.map((area) => (
                              <SelectItem key={area.id} value={area.id}>
                                {area.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select your organization&apos;s primary area of legal
                          practice
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="workArrangements"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Work Arrangements Offered</FormLabel>
                        <div className="space-y-2">
                          {["remote", "hybrid", "in_person"].map(
                            (arrangement) => (
                              <div
                                key={arrangement}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={arrangement}
                                  checked={field.value?.includes(
                                    arrangement as
                                      | "remote"
                                      | "hybrid"
                                      | "in_person"
                                  )}
                                  onCheckedChange={(checked) => {
                                    const current = field.value || []
                                    if (checked) {
                                      field.onChange([...current, arrangement])
                                    } else {
                                      field.onChange(
                                        current.filter((v) => v !== arrangement)
                                      )
                                    }
                                  }}
                                />
                                <Label
                                  htmlFor={arrangement}
                                  className="capitalize"
                                >
                                  {arrangement === "in_person"
                                    ? "In Person"
                                    : arrangement}
                                </Label>
                              </div>
                            )
                          )}
                        </div>
                        <FormDescription>
                          Select all work arrangements you can offer
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxStudents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Students Capacity</FormLabel>
                        <FormControl>
                          <InputSpin
                            {...field}
                            min={1}
                            max={4}
                            onChange={(value) => field.onChange(Number(value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of students you can supervise (up to 4)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="request-form"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="projectDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Briefly describe your projects and what students will work on..."
                            className="resize-none"
                            rows={4}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum 500 characters
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="studentsRequested"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number of Positions</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={20}
                              {...field}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                              field.onChange(parseInt(e.target.value))
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="positionType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Position Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="internship">
                                Internship
                              </SelectItem>
                              <SelectItem value="parttime">
                                Part-time
                              </SelectItem>
                              <SelectItem value="fulltime">
                                Full-time
                              </SelectItem>
                              <SelectItem value="contract">Contract</SelectItem>
                              <SelectItem value="volunteer">
                                Volunteer
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="compensationType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Compensation</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="paid">Paid</SelectItem>
                              <SelectItem value="unpaid">Unpaid</SelectItem>
                              <SelectItem value="credit">
                                Academic Credit
                              </SelectItem>
                              <SelectItem value="stipend">Stipend</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="minimumYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Academic Year</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select year" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="any">Any</SelectItem>
                            <SelectItem value="first">First Year</SelectItem>
                            <SelectItem value="second">Second Year</SelectItem>
                            <SelectItem value="third">Third Year</SelectItem>
                            <SelectItem value="fourth">Fourth Year</SelectItem>
                            <SelectItem value="graduate">Graduate</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <Label>Preferred Majors (Optional)</Label>
                    <div className="space-y-2 mt-2">
                      {preferredMajors.map((major, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <span className="flex-1 px-3 py-2 bg-muted rounded-md text-sm">
                            {major}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeMajor(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <div className="flex gap-2">
                        <Input
                          value={newMajor}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewMajor(e.target.value)}
                          placeholder="e.g., Computer Science, Engineering"
                          {...({ onKeyDown: (e: any) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              addMajor();
                            }
                          }} as any)}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={addMajor}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label>Required Skills (Optional)</Label>
                    <div className="space-y-2 mt-2">
                      {requiredSkills.map((skill, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <span className="flex-1 px-3 py-2 bg-muted rounded-md text-sm">
                            {skill}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeSkill(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <div className="flex gap-2">
                        <Input
                          value={newSkill}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewSkill(e.target.value)}
                          placeholder="e.g., JavaScript, Project Management"
                          {...({ onKeyDown: (e: any) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              addSkill();
                            }
                          }} as any)}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={addSkill}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="areaOfLawId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Area of Law</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select area of law" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {areasOfLaw.map((area) => (
                              <SelectItem key={area.id} value={area.id}>
                                {area.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select your organization&apos;s primary area of legal
                          practice
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="workArrangements"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Work Arrangements Offered</FormLabel>
                        <div className="space-y-2">
                          {["remote", "hybrid", "in_person"].map(
                            (arrangement) => (
                              <div
                                key={arrangement}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={arrangement}
                                  checked={field.value?.includes(
                                    arrangement as
                                      | "remote"
                                      | "hybrid"
                                      | "in_person"
                                  )}
                                  onCheckedChange={(checked) => {
                                    const current = field.value || []
                                    if (checked) {
                                      field.onChange([...current, arrangement])
                                    } else {
                                      field.onChange(
                                        current.filter((v) => v !== arrangement)
                                      )
                                    }
                                  }}
                                />
                                <Label
                                  htmlFor={arrangement}
                                  className="capitalize"
                                >
                                  {arrangement === "in_person"
                                    ? "In Person"
                                    : arrangement}
                                </Label>
                              </div>
                            )
                          )}
                        </div>
                        <FormDescription>
                          Select all work arrangements you can offer
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxStudents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Students Capacity</FormLabel>
                        <FormControl>
                          <InputSpin
                            {...field}
                            min={1}
                            max={4}
                            onChange={(value) => field.onChange(Number(value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of students you can supervise (up to 4)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(0)}
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Complete Onboarding
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
