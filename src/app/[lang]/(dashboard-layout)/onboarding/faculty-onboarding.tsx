"use client"

import { useState } from "react"
import { use<PERSON>outer } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { AnimatePresence, motion } from "framer-motion"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { ChevronLeft, ChevronRight, Loader2, Plus, X } from "lucide-react"

import type { DictionaryType } from "@/lib/get-dictionary"

import { toast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { InputSpin } from "@/components/ui/input-spin"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"

interface FacultyOnboardingProps {
  dictionary: DictionaryType
}

const facultyOnboardingSchema = z.discriminatedUnion("hasOnboardedStudents", [
  z.object({
    hasOnboardedStudents: z.literal(true),
    studentNames: z
      .array(z.string().min(1, "Student name is required"))
      .min(1, "At least one student name is required"),
    autoMatchEnabled: z.boolean().default(false),
    maxStudents: z.number().min(1).max(10).default(1),
  }),
  z.object({
    hasOnboardedStudents: z.literal(false),
    researchDescription: z
      .string()
      .min(10, "Research description must be at least 10 characters")
      .max(500),
    studentsRequested: z.number().min(1).max(10),
    minimumGrade: z.enum(["first", "second", "third", "fourth", "graduate"]),
    minimumGPA: z.number().min(0).max(4).optional(),
    preferredYear: z.enum([
      "any",
      "first",
      "second",
      "third",
      "fourth",
      "graduate",
    ]),
    proficiencies: z.array(z.string().min(1)).optional(),
    autoMatchEnabled: z.boolean().default(true),
    maxStudents: z.number().min(1).max(10).default(1),
  }),
])

type FacultyOnboardingData = z.infer<typeof facultyOnboardingSchema>

export function FacultyOnboarding({
  dictionary: _dictionary,
}: FacultyOnboardingProps) {
  const { data: session, update } = useSession()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [hasOnboardedStudents, setHasOnboardedStudents] = useState<
    boolean | null
  >(null)
  const [studentNames, setStudentNames] = useState<string[]>([])
  const [proficiencies, setProficiencies] = useState<string[]>([])
  const [newProficiency, setNewProficiency] = useState("")

  const form = useForm<FacultyOnboardingData>({
    resolver: zodResolver(facultyOnboardingSchema),
    defaultValues: {
      hasOnboardedStudents: false,
      studentsRequested: 1,
      preferredYear: "any",
      autoMatchEnabled: true,
      maxStudents: 1,
    },
  })

  const handleInitialChoice = (value: boolean) => {
    setHasOnboardedStudents(value)
    form.setValue("hasOnboardedStudents", value)
    setCurrentStep(1)
  }

  const addStudentName = () => {
    setStudentNames([...studentNames, ""])
  }

  const updateStudentName = (index: number, value: string) => {
    const updated = [...studentNames]
    updated[index] = value
    setStudentNames(updated)
    form.setValue(
      "studentNames",
      updated.filter((name) => name.trim() !== "")
    )
  }

  const removeStudentName = (index: number) => {
    const updated = studentNames.filter((_, i) => i !== index)
    setStudentNames(updated)
    form.setValue(
      "studentNames",
      updated.filter((name) => name.trim() !== "")
    )
  }

  const addProficiency = () => {
    if (newProficiency.trim()) {
      const updated = [...proficiencies, newProficiency.trim()]
      setProficiencies(updated)
      form.setValue("proficiencies", updated)
      setNewProficiency("")
    }
  }

  const removeProficiency = (index: number) => {
    const updated = proficiencies.filter((_, i) => i !== index)
    setProficiencies(updated)
    form.setValue("proficiencies", updated)
  }

  const onSubmit = async (data: FacultyOnboardingData) => {
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/onboarding/faculty", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          userId: session?.user?.id,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to complete onboarding")
      }

      // Update session to reflect onboarding completion
      await update()

      toast({
        title: "Welcome to Shadowland!",
        description: "Your faculty profile has been set up successfully.",
      })

      router.push("/")
    } catch (_error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to complete onboarding. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (currentStep === 0) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">Faculty Onboarding</CardTitle>
          <CardDescription>Welcome to the SA1L Program portal</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">
              Have you already onboarded students for this year&apos;s SA1L
              Program?
            </h3>
            <RadioGroup
              onValueChange={(value) => handleInitialChoice(value === "yes")}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="yes" />
                <Label htmlFor="yes">
                  Yes, I have already onboarded students
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="no" />
                <Label htmlFor="no">No, I need to request students</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {hasOnboardedStudents
            ? "List Your Onboarded Students"
            : "Create Student Request"}
        </CardTitle>
        <CardDescription>
          {hasOnboardedStudents
            ? "Please provide the names of students you've already onboarded"
            : "Tell us about your research needs and student requirements"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <AnimatePresence mode="wait">
              {hasOnboardedStudents ? (
                <motion.div
                  key="student-list"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-4"
                >
                  <div>
                    <Label>Student Names</Label>
                    <div className="space-y-2 mt-2">
                      {studentNames.length === 0 && (
                        <p className="text-sm text-muted-foreground">
                          Add at least one student name
                        </p>
                      )}
                      {studentNames.map((name, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={name}
                            onChange={(e) =>
                              updateStudentName(index, e.target.value)
                            }
                            placeholder="Enter student full name"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeStudentName(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addStudentName}
                        className="mt-2"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Student
                      </Button>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="autoMatchEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Enable Auto-Matching
                          </FormLabel>
                          <FormDescription>
                            Allow the system to automatically match students to
                            you
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxStudents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Students</FormLabel>
                        <FormControl>
                          <InputSpin
                            {...field}
                            min={1}
                            max={10}
                            onChange={(value) => field.onChange(Number(value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of students you can supervise
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="request-form"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="researchDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Research Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Briefly describe your research area and what students will work on..."
                            className="resize-none"
                            rows={4}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum 500 characters
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="studentsRequested"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Students Requested</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            max={10}
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="minimumGrade"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Grade Level</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select grade" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="first">First Year</SelectItem>
                              <SelectItem value="second">
                                Second Year
                              </SelectItem>
                              <SelectItem value="third">Third Year</SelectItem>
                              <SelectItem value="fourth">
                                Fourth Year
                              </SelectItem>
                              <SelectItem value="graduate">Graduate</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="minimumGPA"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum GPA (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.1"
                              min={0}
                              max={4}
                              placeholder="3.0"
                              {...field}
                              onChange={(e) =>
                                field.onChange(parseFloat(e.target.value))
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="preferredYear"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Preferred Year</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select year" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="any">Any</SelectItem>
                              <SelectItem value="first">First Year</SelectItem>
                              <SelectItem value="second">
                                Second Year
                              </SelectItem>
                              <SelectItem value="third">Third Year</SelectItem>
                              <SelectItem value="fourth">
                                Fourth Year
                              </SelectItem>
                              <SelectItem value="graduate">Graduate</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <Label>Required Proficiencies (Optional)</Label>
                    <div className="space-y-2 mt-2">
                      {proficiencies.map((prof, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <span className="flex-1 px-3 py-2 bg-muted rounded-md text-sm">
                            {prof}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeProficiency(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <div className="flex gap-2">
                        <Input
                          value={newProficiency}
                          onChange={(e) => setNewProficiency(e.target.value)}
                          placeholder="e.g., Python, Data Analysis, Lab Experience"
                          {...({ onKeyDown: (e: any) =>
                            e.key === "Enter" &&
                            (e.preventDefault(), addProficiency())
                          } as any)}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={addProficiency}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="autoMatchEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Enable Auto-Matching
                          </FormLabel>
                          <FormDescription>
                            Allow the system to automatically match students to
                            your research projects
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxStudents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Students Capacity</FormLabel>
                        <FormControl>
                          <InputSpin
                            {...field}
                            min={1}
                            max={10}
                            onChange={(value) => field.onChange(Number(value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of students you can supervise at once
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(0)}
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Complete Onboarding
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
