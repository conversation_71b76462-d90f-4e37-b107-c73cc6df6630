import { relations } from "drizzle-orm"
import {
  boolean,
  date,
  index,
  integer,
  jsonb,
  numeric,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
  uuid,
  // varchar,
} from "drizzle-orm/pg-core"

// Users table
export const users = pgTable(
  "users",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    name: text("name").notNull(),
    firstName: text("first_name"),
    lastName: text("last_name"),
    username: text("username").unique(),
    role: text("role").notNull(),
    organizationName: text("organization_name"), // For organization role
    organizationTitle: text("organization_title"), // For organization role
    email: text("email").unique(),
    emailVerifyToken: text("email_verify_token"),
    emailVerified: timestamp("email_verified", { mode: "date" }),
    password: text("password"),
    passwordResetToken: text("password_reset_token"),
    passwordResetExpires: timestamp("password_reset_expires", { mode: "date" }),
    avatar: text("avatar"),
    profileBackground: text("profile_background"),
    status: text("status").default("ONLINE").notNull(),
    // 2FA fields
    totpSecret: text("totp_secret"), // Encrypted TOTP secret key
    totpEnabled: boolean("totp_enabled").default(false).notNull(),
    totpBackupCodes: jsonb("totp_backup_codes").$type<string[]>(), // Encrypted backup codes
    totpLastUsed: timestamp("totp_last_used", { mode: "date" }), // Prevent token reuse
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => ({
    nameUsernameStatusCreatedAtIdx: index(
      "name_username_status_created_at_idx"
    ).on(table.name, table.username, table.status, table.createdAt),
  })
)

// User Preferences table
export const userPreferences = pgTable("user_preferences", {
  id: uuid("id").primaryKey().defaultRandom(),
  theme: text("theme").notNull(),
  mode: text("mode").notNull(),
  radius: text("radius").notNull(),
  layout: text("layout").notNull(),
  direction: text("direction").notNull(),
  userId: uuid("user_id")
    .unique()
    .references(() => users.id, { onDelete: "cascade" }),
})

// Accounts table (for NextAuth)
export const accounts = pgTable(
  "accounts",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    type: text("type").notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("provider_account_id").notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => ({
    providerProviderAccountIdUnique: unique().on(
      table.provider,
      table.providerAccountId
    ),
  })
)

// Sessions table (for NextAuth)
export const sessions = pgTable("sessions", {
  id: uuid("id").primaryKey().defaultRandom(),
  sessionToken: text("session_token").unique().notNull(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expires: timestamp("expires", { mode: "date" }).notNull(),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Verification Tokens table (for NextAuth)
export const verificationTokens = pgTable(
  "verification_tokens",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (table) => ({
    identifierTokenPrimary: primaryKey({
      columns: [table.identifier, table.token],
    }),
  })
)

// Files table (for file uploads)
export const files = pgTable("files", {
  id: uuid("id").primaryKey().defaultRandom(),
  filename: text("filename").notNull(),
  originalName: text("original_name").notNull(),
  mimeType: text("mime_type").notNull(),
  size: integer("size").notNull(),
  url: text("url").notNull(),
  s3Key: text("s3_key"),
  uploadedBy: uuid("uploaded_by")
    .notNull()
    .references(() => users.id),
  purpose: text("purpose"), // 'transcript', 'deliverable', 'avatar', etc.
  metadata: jsonb("metadata"),
  // Virus scanning fields
  virusScanStatus: text("virus_scan_status", {
    enum: ["pending", "scanning", "clean", "infected", "failed"],
  }).default("pending"),
  virusScanId: text("virus_scan_id"), // VirusTotal scan ID
  virusScanDate: timestamp("virus_scan_date", { mode: "date" }),
  virusThreatCount: integer("virus_threat_count").default(0),
  virusEnginesCount: integer("virus_engines_count").default(0),
  virusScanDetails: jsonb("virus_scan_details"), // Full VirusTotal scan results
  virusPermalink: text("virus_permalink"), // VirusTotal results URL
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Areas of Law table - dynamic list managed by admin
export const areasOfLaw = pgTable("areas_of_law", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull().unique(),
  description: text("description"),
  isActive: boolean("is_active").notNull().default(true),
  displayOrder: integer("display_order").notNull().default(0),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Geographic Locations table - dynamic list managed by admin
export const geographicLocations = pgTable("geographic_locations", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull().unique(),
  province: text("province"),
  country: text("country").notNull().default("Canada"),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Email Templates table - admin-editable email templates
export const emailTemplates = pgTable("email_templates", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull().unique(), // e.g., 'application_confirmation', 'student_match_notification'
  description: text("description"), // Human-readable description for admin UI
  subject: text("subject").notNull(), // Email subject line template
  htmlContent: text("html_content").notNull(), // HTML email template
  textContent: text("text_content").notNull(), // Plain text email template
  variables: jsonb("variables").$type<string[]>(), // Available variables: ['studentName', 'organizationName', etc.]
  category: text("category").notNull(), // 'authentication' | 'application' | 'matching' | 'interview' | 'deliverable' | 'notification'
  isActive: boolean("is_active").notNull().default(true),
  isSystem: boolean("is_system").notNull().default(false), // Cannot be deleted if true
  version: integer("version").notNull().default(1), // For versioning templates
  lastUsedAt: timestamp("last_used_at", { mode: "date" }), // Track usage
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Student Grades table - stores individual course grades
export const studentGrades = pgTable("student_grades", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id")
    .notNull()
    .references(() => users.id),
  courseName: text("course_name").notNull(),
  courseCode: text("course_code"),
  grade: text("grade").notNull(), // Letter grade: A+, A, A-, B+, etc.
  gradeValue: numeric("grade_value", { precision: 3, scale: 2 }), // Numeric value: 5.0, 4.5, etc.
  credits: integer("credits").default(5),
  term: text("term"), // Fall, Winter, etc.
  year: integer("year"),
  isCore: boolean("is_core").notNull().default(false), // Core doctrinal course
  isLRW: boolean("is_lrw").notNull().default(false), // Legal Research & Writing
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
})

// Student Statements table - stores area-specific statements
export const studentStatements = pgTable("student_statements", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id")
    .notNull()
    .references(() => users.id),
  areaOfLawId: uuid("area_of_law_id")
    .notNull()
    .references(() => areasOfLaw.id),
  statement: text("statement").notNull(), // 350-word statement
  rank: integer("rank"), // 1-5 ranking for this area
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Student Profiles table
export const studentProfiles = pgTable("student_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id)
    .unique(),
  hasBeenMatched: boolean("has_been_matched").notNull().default(false),
  matchedReference: text("matched_reference"),
  matchedType: text("matched_type"), // 'faculty' | 'organization'
  matchStatus: text("match_status").notNull().default("pending"), // 'pending' | 'matched' | 'rejected' | 'confirmed'
  matchedOrganizationId: uuid("matched_organization_id").references(
    () => users.id
  ),
  matchedFacultyId: uuid("matched_faculty_id").references(() => users.id),
  // Placement preferences
  placementTypes: jsonb("placement_types"), // Array of: 'research_assistantship' | 'externship' | 'self_directed'
  preferredFacultyIds: jsonb("preferred_faculty_ids"), // Array of faculty user IDs for RA applications
  selfDirectedProposal: jsonb("self_directed_proposal"), // { organizationName, supervisorName, supervisorEmail, description, nonprofit: boolean }
  // Application status
  hasApplied: boolean("has_applied").notNull().default(false),
  applicationDate: timestamp("application_date", { mode: "date" }),
  draftApplication: jsonb("draft_application"), // Stores application form data as draft
  // File references
  resumeId: uuid("resume_id").references(() => files.id), // Legacy field
  coverLetterId: uuid("cover_letter_id").references(() => files.id), // Legacy field
  resumeFileId: uuid("resume_file_id").references(() => files.id),
  coverLetterFileId: uuid("cover_letter_file_id").references(() => files.id),
  academicYear: text("academic_year"), // 'first' | 'second' | 'third' | 'fourth' | 'graduate'
  major: text("major"),
  gpa: numeric("gpa", { precision: 3, scale: 2 }),
  skills: jsonb("skills"), // Array of skills
  interests: text("interests"),
  availability: text("availability"), // 'fulltime' | 'parttime' | 'flexible'
  program: text("program"), // 'undergraduate' | 'graduate' | 'phd'
  studentId: text("student_id"), // Student ID number
  statement: text("statement"), // Personal statement
  statementScore: integer("statement_score").default(0),
  // Area of law rankings - student ranks 5 out of 9 areas
  areaOfLawRankings: jsonb("area_of_law_rankings"), // { areaId: rank } where rank is 1-5
  // Geographic preferences
  geographicPreferences: jsonb("geographic_preferences"), // Array of location IDs
  // Work arrangement preferences
  workArrangementPreference: jsonb("work_arrangement_preference"), // Array of: 'in_person' | 'remote' | 'hybrid'
  // Grade data
  coreGPA: numeric("core_gpa", { precision: 3, scale: 2 }), // Calculated from core courses
  lrwGPA: numeric("lrw_gpa", { precision: 3, scale: 2 }), // Legal Research & Writing GPA
  overallGPA: numeric("overall_gpa", { precision: 3, scale: 2 }), // Overall GPA
  transcriptId: uuid("transcript_id").references(() => files.id), // Legacy field
  transcriptFileId: uuid("transcript_file_id").references(() => files.id),
  // Start date for deliverable calculations
  placementStartDate: date("placement_start_date", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Faculty Profiles table
export const facultyProfiles = pgTable("faculty_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id)
    .unique(),
  department: text("department"),
  position: text("position"),
  researchAreas: jsonb("research_areas"), // Array of research areas
  bio: text("bio"),
  officeLocation: text("office_location"),
  officeHours: text("office_hours"),
  canSuperviseStudents: boolean("can_supervise_students")
    .notNull()
    .default(true),
  maxStudents: integer("max_students").default(5),
  currentStudentsCount: integer("current_students_count").notNull().default(0),
  // Research projects - array of { title, description, requirements }
  researchProjects: jsonb("research_projects").default([]),
  // Auto-match preference - if true, admin can auto-match students
  autoMatchEnabled: boolean("auto_match_enabled").notNull().default(false),
  // Area of law for faculty research (optional)
  areaOfLawId: uuid("area_of_law_id").references(() => areasOfLaw.id),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Organization Profiles table
export const organizationProfiles = pgTable("organization_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id)
    .unique(),
  organizationType: text("organization_type"), // 'nonprofit' | 'government' | 'corporate' | 'startup'
  industry: text("industry"),
  size: text("size"), // 'small' | 'medium' | 'large'
  website: text("website"),
  description: text("description"),
  // Area of law for this organization
  areaOfLawId: uuid("area_of_law_id").references(() => areasOfLaw.id),
  // Work arrangements offered
  workArrangements: jsonb("work_arrangements"), // Array of: 'in_person' | 'remote' | 'hybrid'
  // Geographic location
  locationId: uuid("location_id").references(() => geographicLocations.id),
  // Capacity
  maxStudents: integer("max_students").default(1),
  currentStudentsCount: integer("current_students_count").notNull().default(0),
  lookingFor: jsonb("looking_for"), // Array of skills/qualifications
  projectTypes: jsonb("project_types"), // Array of project types
  canSponsorInternships: boolean("can_sponsor_internships")
    .notNull()
    .default(false),
  paidOpportunities: boolean("paid_opportunities").notNull().default(false),
  remoteAvailable: boolean("remote_available").notNull().default(false),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Projects table
export const projects = pgTable("projects", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: text("title").notNull(),
  description: text("description"),
  organizationId: uuid("organization_id").references(() => users.id),
  facultyId: uuid("faculty_id").references(() => users.id),
  status: text("status").notNull().default("pending"), // 'pending' | 'active' | 'completed' | 'cancelled'
  startDate: timestamp("start_date", { mode: "date" }),
  endDate: timestamp("end_date", { mode: "date" }),
  requirements: jsonb("requirements"),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Project Faculty junction table (for co-supervision)
export const projectFaculty = pgTable(
  "project_faculty",
  {
    projectId: uuid("project_id")
      .notNull()
      .references(() => projects.id, { onDelete: "cascade" }),
    facultyId: uuid("faculty_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    isPrimary: boolean("is_primary").notNull().default(false), // Primary supervisor flag
    assignedAt: timestamp("assigned_at", { mode: "date" })
      .defaultNow()
      .notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.projectId, table.facultyId] }),
  })
)

// Project Students junction table
export const projectStudents = pgTable(
  "project_students",
  {
    projectId: uuid("project_id")
      .notNull()
      .references(() => projects.id),
    studentId: uuid("student_id")
      .notNull()
      .references(() => users.id),
    assignedAt: timestamp("assigned_at", { mode: "date" })
      .defaultNow()
      .notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.projectId, table.studentId] }),
  })
)

// Cycles table for managing program cycles
export const cycles = pgTable("cycles", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(), // e.g., "Summer 2025"
  year: integer("year").notNull(),
  term: text("term").notNull(), // 'summer' | 'fall' | 'winter'
  startDate: timestamp("start_date", { mode: "date" }).notNull(),
  endDate: timestamp("end_date", { mode: "date" }).notNull(),
  applicationOpenDate: timestamp("application_open_date", {
    mode: "date",
  }).notNull(),
  applicationCloseDate: timestamp("application_close_date", {
    mode: "date",
  }).notNull(),
  matchingDate: timestamp("matching_date", { mode: "date" }),
  isActive: boolean("is_active").default(true),
  isCurrent: boolean("is_current").default(false), // Only one cycle can be current
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Deliverables table (updated for Phase 9)
export const deliverables = pgTable("deliverables", {
  id: uuid("id").primaryKey().defaultRandom(),
  projectId: uuid("project_id").references(() => projects.id),
  studentId: uuid("student_id")
    .notNull()
    .references(() => users.id),
  facultyId: uuid("faculty_id").references(() => users.id),
  organizationId: uuid("organization_id").references(() => users.id),
  cycleId: uuid("cycle_id").references(() => cycles.id),
  templateId: uuid("template_id").references(() => deliverableTemplates.id),
  title: text("title").notNull(),
  description: text("description"),
  type: text("type").notNull(), // 'learning_plan' | 'midpoint_checkin' | 'final_reflection' | 'custom'
  status: text("status").notNull().default("pending"), // 'pending' | 'submitted' | 'under_review' | 'approved' | 'needs_revision'
  dueDate: timestamp("due_date", { mode: "date" }).notNull(),
  submittedAt: timestamp("submitted_at", { mode: "date" }),
  fileId: uuid("file_id").references(() => files.id), // Deprecated, use revisions
  currentRevisionId: uuid("current_revision_id"), // Points to latest revision
  feedback: text("feedback"), // Deprecated, use revisions
  grade: integer("grade"),
  reviewedBy: uuid("reviewed_by").references(() => users.id),
  reviewedAt: timestamp("reviewed_at", { mode: "date" }),
  // Conflict resolution fields
  lastModifiedBy: uuid("last_modified_by").references(() => users.id),
  versionNumber: integer("version_number").notNull().default(1),
  editSessionId: uuid("edit_session_id"),
  editStartedAt: timestamp("edit_started_at", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Deliverable Templates table (Phase 9)
export const deliverableTemplates = pgTable("deliverable_templates", {
  id: uuid("id").primaryKey().defaultRandom(),
  cycleId: uuid("cycle_id").references(() => cycles.id, {
    onDelete: "cascade",
  }),
  name: text("name").notNull(),
  description: text("description").notNull(),
  type: text("type").notNull(), // 'learning_plan' | 'midpoint_checkin' | 'final_reflection' | 'custom'
  daysFromStart: integer("days_from_start").notNull(), // Days after placement start date
  instructions: text("instructions").notNull(),
  requiresAttachment: boolean("requires_attachment").default(false),
  maxAttachments: integer("max_attachments").default(1),
  requiresApproval: boolean("requires_approval").default(true),
  isActive: boolean("is_active").default(true),
  displayOrder: integer("display_order").notNull(),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Deliverable Revisions table (Phase 9)
export const deliverableRevisions = pgTable("deliverable_revisions", {
  id: uuid("id").primaryKey().defaultRandom(),
  deliverableId: uuid("deliverable_id")
    .notNull()
    .references(() => deliverables.id, { onDelete: "cascade" }),
  version: integer("version").notNull().default(1),
  content: text("content"),
  attachments: jsonb("attachments")
    .$type<
      Array<{
        id: string
        name: string
        url: string
        size: number
        mimeType: string
        uploadedAt: string
      }>
    >()
    .default([]),
  submittedAt: timestamp("submitted_at", { mode: "date" })
    .defaultNow()
    .notNull(),
  submittedBy: uuid("submitted_by").references(() => users.id),
  feedback: text("feedback"),
  feedbackBy: uuid("feedback_by").references(() => users.id),
  feedbackAt: timestamp("feedback_at", { mode: "date" }),
  status: text("status").notNull().default("draft"), // 'draft' | 'submitted' | 'under_review' | 'needs_revision' | 'approved'
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
})

// Deliverable Drafts table for auto-save (Phase 9)
export const deliverableDrafts = pgTable("deliverable_drafts", {
  id: uuid("id").primaryKey().defaultRandom(),
  deliverableId: uuid("deliverable_id")
    .notNull()
    .references(() => deliverables.id, { onDelete: "cascade" }),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id),
  content: text("content"),
  attachments: jsonb("attachments")
    .$type<
      Array<{
        id: string
        name: string
        url: string
        size: number
        mimeType: string
        uploadedAt: string
      }>
    >()
    .default([]),
  lastSavedAt: timestamp("last_saved_at", { mode: "date" })
    .defaultNow()
    .notNull(),
  // Conflict resolution fields
  clientVersion: integer("client_version").notNull().default(1),
  serverVersion: integer("server_version").notNull().default(1),
  conflictDetected: boolean("conflict_detected").default(false),
  lastConflictAt: timestamp("last_conflict_at", { mode: "date" }),
})

// Organization Mentor Delegations table (Phase 9)
export const deliverableDelegations = pgTable("deliverable_delegations", {
  id: uuid("id").primaryKey().defaultRandom(),
  deliverableId: uuid("deliverable_id")
    .notNull()
    .references(() => deliverables.id, { onDelete: "cascade" }),
  delegatedFrom: uuid("delegated_from")
    .notNull()
    .references(() => users.id),
  delegatedTo: uuid("delegated_to")
    .notNull()
    .references(() => users.id),
  delegatedAt: timestamp("delegated_at", { mode: "date" })
    .defaultNow()
    .notNull(),
  reason: text("reason"),
  isActive: boolean("is_active").default(true),
})

// Conflict Resolution Log table
export const conflictResolutionLog = pgTable("conflict_resolution_log", {
  id: uuid("id").primaryKey().defaultRandom(),
  entityType: text("entity_type").notNull(), // 'deliverable' | 'project' | 'profile' | etc
  entityId: uuid("entity_id").notNull(),
  conflictType: text("conflict_type").notNull(), // 'concurrent_edit' | 'version_mismatch' | 'edit_session_expired'
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id),
  conflictingUserId: uuid("conflicting_user_id").references(() => users.id),
  clientVersion: integer("client_version"),
  serverVersion: integer("server_version"),
  resolutionStrategy: text("resolution_strategy"), // 'user_merge' | 'server_wins' | 'client_wins' | 'manual'
  resolutionData: jsonb("resolution_data"),
  resolvedAt: timestamp("resolved_at", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
})

// Support Tickets table
export const supportTickets = pgTable("support_tickets", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id),
  subject: text("subject").notNull(),
  category: text("category").notNull(), // 'technical' | 'academic' | 'administrative' | 'other'
  priority: text("priority").notNull().default("medium"), // 'low' | 'medium' | 'high' | 'urgent'
  status: text("status").notNull().default("open"), // 'open' | 'in_progress' | 'resolved' | 'closed'
  description: text("description").notNull(),
  assignedTo: uuid("assigned_to").references(() => users.id),
  resolvedAt: timestamp("resolved_at", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Support Ticket Messages table
export const supportTicketMessages = pgTable("support_ticket_messages", {
  id: uuid("id").primaryKey().defaultRandom(),
  ticketId: uuid("ticket_id")
    .notNull()
    .references(() => supportTickets.id),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id),
  message: text("message").notNull(),
  isInternal: boolean("is_internal").notNull().default(false),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
})

// Interviews table
export const interviews = pgTable("interviews", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: text("title").notNull(),
  organizationId: uuid("organization_id")
    .notNull()
    .references(() => users.id),
  studentId: uuid("student_id")
    .notNull()
    .references(() => users.id),
  scheduledDate: timestamp("scheduled_date", { mode: "date" }).notNull(),
  duration: integer("duration").notNull(), // in minutes
  meetingType: text("meeting_type").notNull(), // 'virtual' | 'in_person'
  meetingLink: text("meeting_link"),
  location: text("location"),
  status: text("status").notNull().default("scheduled"), // 'scheduled' | 'completed' | 'cancelled' | 'no_show'
  notes: text("notes"),
  studentConfirmed: boolean("student_confirmed").notNull().default(false),
  organizationConfirmed: boolean("organization_confirmed")
    .notNull()
    .default(false),
  reminderSent: boolean("reminder_sent").notNull().default(false),
  outlookEventId: text("outlook_event_id"),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Student Applications table (main application record)
export const studentApplications = pgTable("student_applications", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id")
    .notNull()
    .references(() => users.id),
  applicationDate: timestamp("application_date", { mode: "date" }).notNull(),
  status: text("status").notNull().default("submitted"), // submitted, under_review, matched, rejected
  streams: jsonb("streams").notNull(), // { externship: boolean, research: boolean, selfProposed: boolean }
  cvFileId: uuid("cv_file_id").references(() => files.id),
  coverLetterFileId: uuid("cover_letter_file_id").references(() => files.id),
  transcriptFileId: uuid("transcript_file_id").references(() => files.id),
  geographicPreferences: jsonb("geographic_preferences").notNull(), // array of location IDs
  workArrangement: text("work_arrangement").notNull(), // in-person, remote, hybrid
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Student Area of Law Rankings
export const studentAreaOfLawRankings = pgTable(
  "student_area_of_law_rankings",
  {
    studentId: uuid("student_id")
      .notNull()
      .references(() => users.id),
    areaOfLawId: uuid("area_of_law_id")
      .notNull()
      .references(() => areasOfLaw.id),
    ranking: integer("ranking").notNull(), // 1-5 (1 being most preferred)
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.studentId, table.areaOfLawId] }),
  })
)

// Student Research Interests (faculty projects they're interested in)
export const studentResearchInterests = pgTable(
  "student_research_interests",
  {
    studentId: uuid("student_id")
      .notNull()
      .references(() => users.id),
    facultyId: uuid("faculty_id")
      .notNull()
      .references(() => users.id),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.studentId, table.facultyId] }),
  })
)

// Student Self-Proposed Placements
export const studentSelfProposedPlacements = pgTable(
  "student_self_proposed_placements",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    studentId: uuid("student_id")
      .notNull()
      .references(() => users.id),
    organizationName: text("organization_name").notNull(),
    areaOfLaw: text("area_of_law").notNull(),
    supervisorName: text("supervisor_name").notNull(),
    supervisorTitle: text("supervisor_title").notNull(),
    supervisorEmail: text("supervisor_email").notNull(),
    supervisorPhone: text("supervisor_phone").notNull(),
    proposal: text("proposal").notNull(),
    status: text("status").notNull().default("pending"), // pending, approved, rejected
    reviewedBy: uuid("reviewed_by").references(() => users.id),
    reviewedAt: timestamp("reviewed_at", { mode: "date" }),
    reviewFeedback: text("review_feedback"),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
  }
)

// Student Application Drafts (for auto-save functionality)
export const studentApplicationDrafts = pgTable("student_application_drafts", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id)
    .unique(), // Only one draft per user
  currentStep: integer("current_step").notNull().default(0),
  formData: jsonb("form_data").notNull(), // Stores the entire form state
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
})

// Training Modules (configurable training content)
export const trainingModules = pgTable(
  "training_modules",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    title: text("title").notNull(),
    description: text("description").notNull(),
    content: text("content"), // Rich text content or external URL
    contentType: text("content_type").notNull().default("internal"), // internal, external_url, video
    externalUrl: text("external_url"), // For external training content
    isRequired: boolean("is_required").notNull().default(false),
    minimumScore: integer("minimum_score"), // For assessments (percentage)
    durationMinutes: integer("duration_minutes"), // Estimated completion time
    validFromDate: timestamp("valid_from_date", { mode: "date" }),
    validUntilDate: timestamp("valid_until_date", { mode: "date" }), // For recurring training
    targetRoles: jsonb("target_roles")
      .$type<string[]>()
      .notNull()
      .default(["student"]), // Which user roles must complete
    isActive: boolean("is_active").notNull().default(true),
    displayOrder: integer("display_order").notNull().default(0),
    createdBy: uuid("created_by")
      .notNull()
      .references(() => users.id),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => ({
    titleIdx: index("training_modules_title_idx").on(table.title),
    isRequiredIdx: index("training_modules_is_required_idx").on(
      table.isRequired
    ),
    targetRolesIdx: index("training_modules_target_roles_idx").on(
      table.targetRoles
    ),
  })
)

// User Training Progress (tracks completion status)
export const userTrainingProgress = pgTable(
  "user_training_progress",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id),
    trainingModuleId: uuid("training_module_id")
      .notNull()
      .references(() => trainingModules.id),
    status: text("status").notNull().default("not_started"), // not_started, in_progress, completed, failed, expired
    progress: integer("progress").notNull().default(0), // 0-100 percentage
    score: integer("score"), // Final score if assessment
    startedAt: timestamp("started_at", { mode: "date" }),
    completedAt: timestamp("completed_at", { mode: "date" }),
    lastAccessedAt: timestamp("last_accessed_at", { mode: "date" }),
    timeSpentMinutes: integer("time_spent_minutes").notNull().default(0),
    attempts: integer("attempts").notNull().default(0), // Number of completion attempts
    certificateIssued: boolean("certificate_issued").notNull().default(false),
    notes: text("notes"), // Admin notes or user feedback
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => ({
    userModuleUnique: unique().on(table.userId, table.trainingModuleId),
    userIdx: index("user_training_progress_user_idx").on(table.userId),
    statusIdx: index("user_training_progress_status_idx").on(table.status),
    completedAtIdx: index("user_training_progress_completed_at_idx").on(
      table.completedAt
    ),
  })
)

// Define relations
export const usersRelations = relations(users, ({ one, many }) => ({
  preferences: one(userPreferences, {
    fields: [users.id],
    references: [userPreferences.userId],
  }),
  accounts: many(accounts),
  sessions: many(sessions),
  files: many(files),
  studentProfile: one(studentProfiles, {
    fields: [users.id],
    references: [studentProfiles.userId],
  }),
  facultyProfile: one(facultyProfiles, {
    fields: [users.id],
    references: [facultyProfiles.userId],
  }),
  organizationProfile: one(organizationProfiles, {
    fields: [users.id],
    references: [organizationProfiles.userId],
  }),
  organizationProjects: many(projects),
  facultyProjects: many(projects),
  // New relation for co-supervision
  facultyProjectMemberships: many(projectFaculty),
  studentProjects: many(projectStudents),
  deliverables: many(deliverables),
  supportTickets: many(supportTickets),
  supportMessages: many(supportTicketMessages),
  organizationInterviews: many(interviews),
  studentInterviews: many(interviews),
  studentGrades: many(studentGrades),
  studentStatements: many(studentStatements),
  studentMatches: many(matches),
  organizationMatches: many(matches),
  facultyMatches: many(matches),
  studentApplications: many(studentApplications),
  studentAreaOfLawRankings: many(studentAreaOfLawRankings),
  studentResearchInterests: many(studentResearchInterests),
  facultyResearchInterests: many(studentResearchInterests),
  studentSelfProposedPlacements: many(studentSelfProposedPlacements),
  reviewedSelfProposedPlacements: many(studentSelfProposedPlacements),
  // Training relations
  trainingProgress: many(userTrainingProgress),
  createdTrainingModules: many(trainingModules),
}))

export const userPreferencesRelations = relations(
  userPreferences,
  ({ one }) => ({
    user: one(users, {
      fields: [userPreferences.userId],
      references: [users.id],
    }),
  })
)

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}))

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}))

export const filesRelations = relations(files, ({ one }) => ({
  uploadedBy: one(users, {
    fields: [files.uploadedBy],
    references: [users.id],
  }),
}))

export const studentProfilesRelations = relations(
  studentProfiles,
  ({ one }) => ({
    user: one(users, {
      fields: [studentProfiles.userId],
      references: [users.id],
    }),
    resume: one(files, {
      fields: [studentProfiles.resumeId],
      references: [files.id],
    }),
    coverLetter: one(files, {
      fields: [studentProfiles.coverLetterId],
      references: [files.id],
    }),
    transcript: one(files, {
      fields: [studentProfiles.transcriptId],
      references: [files.id],
    }),
    resumeFile: one(files, {
      fields: [studentProfiles.resumeFileId],
      references: [files.id],
    }),
    coverLetterFile: one(files, {
      fields: [studentProfiles.coverLetterFileId],
      references: [files.id],
    }),
    transcriptFile: one(files, {
      fields: [studentProfiles.transcriptFileId],
      references: [files.id],
    }),
    matchedOrganization: one(users, {
      fields: [studentProfiles.matchedOrganizationId],
      references: [users.id],
    }),
    matchedFaculty: one(users, {
      fields: [studentProfiles.matchedFacultyId],
      references: [users.id],
    }),
  })
)

export const facultyProfilesRelations = relations(
  facultyProfiles,
  ({ one }) => ({
    user: one(users, {
      fields: [facultyProfiles.userId],
      references: [users.id],
    }),
    areaOfLaw: one(areasOfLaw, {
      fields: [facultyProfiles.areaOfLawId],
      references: [areasOfLaw.id],
    }),
  })
)

export const organizationProfilesRelations = relations(
  organizationProfiles,
  ({ one }) => ({
    user: one(users, {
      fields: [organizationProfiles.userId],
      references: [users.id],
    }),
    areaOfLaw: one(areasOfLaw, {
      fields: [organizationProfiles.areaOfLawId],
      references: [areasOfLaw.id],
    }),
    location: one(geographicLocations, {
      fields: [organizationProfiles.locationId],
      references: [geographicLocations.id],
    }),
  })
)

export const projectsRelations = relations(projects, ({ one, many }) => ({
  organization: one(users, {
    fields: [projects.organizationId],
    references: [users.id],
  }),
  // Legacy single faculty relation - kept for backwards compatibility
  faculty: one(users, {
    fields: [projects.facultyId],
    references: [users.id],
  }),
  // New relation for multiple faculty through junction table
  facultyMembers: many(projectFaculty),
  students: many(projectStudents),
  deliverables: many(deliverables),
  matches: many(matches),
}))

export const projectFacultyRelations = relations(projectFaculty, ({ one }) => ({
  project: one(projects, {
    fields: [projectFaculty.projectId],
    references: [projects.id],
  }),
  faculty: one(users, {
    fields: [projectFaculty.facultyId],
    references: [users.id],
  }),
}))

export const projectStudentsRelations = relations(
  projectStudents,
  ({ one }) => ({
    project: one(projects, {
      fields: [projectStudents.projectId],
      references: [projects.id],
    }),
    student: one(users, {
      fields: [projectStudents.studentId],
      references: [users.id],
    }),
  })
)

export const cyclesRelations = relations(cycles, ({ many }) => ({
  deliverables: many(deliverables),
  deliverableTemplates: many(deliverableTemplates),
}))

export const deliverablesRelations = relations(
  deliverables,
  ({ one, many }) => ({
    project: one(projects, {
      fields: [deliverables.projectId],
      references: [projects.id],
    }),
    student: one(users, {
      fields: [deliverables.studentId],
      references: [users.id],
    }),
    faculty: one(users, {
      fields: [deliverables.facultyId],
      references: [users.id],
    }),
    organization: one(users, {
      fields: [deliverables.organizationId],
      references: [users.id],
    }),
    cycle: one(cycles, {
      fields: [deliverables.cycleId],
      references: [cycles.id],
    }),
    template: one(deliverableTemplates, {
      fields: [deliverables.templateId],
      references: [deliverableTemplates.id],
    }),
    file: one(files, {
      fields: [deliverables.fileId],
      references: [files.id],
    }),
    reviewedByUser: one(users, {
      fields: [deliverables.reviewedBy],
      references: [users.id],
    }),
    revisions: many(deliverableRevisions),
    drafts: many(deliverableDrafts),
    delegations: many(deliverableDelegations),
  })
)

// Deliverable Templates Relations
export const deliverableTemplatesRelations = relations(
  deliverableTemplates,
  ({ one, many }) => ({
    cycle: one(cycles, {
      fields: [deliverableTemplates.cycleId],
      references: [cycles.id],
    }),
    deliverables: many(deliverables),
  })
)

// Deliverable Revisions Relations
export const deliverableRevisionsRelations = relations(
  deliverableRevisions,
  ({ one }) => ({
    deliverable: one(deliverables, {
      fields: [deliverableRevisions.deliverableId],
      references: [deliverables.id],
    }),
    submittedByUser: one(users, {
      fields: [deliverableRevisions.submittedBy],
      references: [users.id],
    }),
    feedbackByUser: one(users, {
      fields: [deliverableRevisions.feedbackBy],
      references: [users.id],
    }),
  })
)

// Deliverable Drafts Relations
export const deliverableDraftsRelations = relations(
  deliverableDrafts,
  ({ one }) => ({
    deliverable: one(deliverables, {
      fields: [deliverableDrafts.deliverableId],
      references: [deliverables.id],
    }),
    user: one(users, {
      fields: [deliverableDrafts.userId],
      references: [users.id],
    }),
  })
)

// Deliverable Delegations Relations
export const deliverableDelegationsRelations = relations(
  deliverableDelegations,
  ({ one }) => ({
    deliverable: one(deliverables, {
      fields: [deliverableDelegations.deliverableId],
      references: [deliverables.id],
    }),
    delegatedFromUser: one(users, {
      fields: [deliverableDelegations.delegatedFrom],
      references: [users.id],
    }),
    delegatedToUser: one(users, {
      fields: [deliverableDelegations.delegatedTo],
      references: [users.id],
    }),
  })
)

export const supportTicketsRelations = relations(
  supportTickets,
  ({ one, many }) => ({
    user: one(users, {
      fields: [supportTickets.userId],
      references: [users.id],
    }),
    assignedTo: one(users, {
      fields: [supportTickets.assignedTo],
      references: [users.id],
    }),
    messages: many(supportTicketMessages),
  })
)

export const supportTicketMessagesRelations = relations(
  supportTicketMessages,
  ({ one }) => ({
    ticket: one(supportTickets, {
      fields: [supportTicketMessages.ticketId],
      references: [supportTickets.id],
    }),
    user: one(users, {
      fields: [supportTicketMessages.userId],
      references: [users.id],
    }),
  })
)

export const interviewsRelations = relations(interviews, ({ one }) => ({
  organization: one(users, {
    fields: [interviews.organizationId],
    references: [users.id],
  }),
  student: one(users, {
    fields: [interviews.studentId],
    references: [users.id],
  }),
}))

// Matches table for tracking student-organization/faculty pairings
export const matches = pgTable("matches", {
  id: uuid("id").defaultRandom().primaryKey(),
  studentId: uuid("student_id")
    .notNull()
    .references(() => users.id),
  // Match is EITHER with organization OR faculty project
  matchType: text("match_type", {
    enum: ["organization", "faculty"],
  }).notNull(),
  organizationId: uuid("organization_id").references(() => users.id),
  // For faculty matches, we now reference the project (which can have multiple faculty)
  projectId: uuid("project_id").references(() => projects.id),
  // Legacy field - kept for backwards compatibility but will be deprecated
  facultyId: uuid("faculty_id").references(() => users.id),
  score: integer("score").notNull(), // Match score 0-100
  status: text("status", {
    enum: [
      "pending",
      "student_accepted",
      "student_rejected",
      "supervisor_accepted",
      "supervisor_rejected",
      "confirmed",
      "cancelled",
    ],
  })
    .notNull()
    .default("pending"),
  // Two-sided acceptance tracking
  studentResponse: text("student_response", {
    enum: ["pending", "accepted", "rejected"],
  }).default("pending"),
  supervisorResponse: text("supervisor_response", {
    enum: ["pending", "accepted", "rejected"],
  }).default("pending"),
  // Scoring breakdown
  scoringDetails: jsonb("scoring_details"), // { areaPreference: 40, statementScore: 25, coreGPA: 25, lrwGPA: 10 }
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  studentRespondedAt: timestamp("student_responded_at"),
  supervisorRespondedAt: timestamp("supervisor_responded_at"),
  confirmedAt: timestamp("confirmed_at"),
  // Placement dates (Phase 9)
  placementStartDate: timestamp("placement_start_date"),
  placementEndDate: timestamp("placement_end_date"),
  approvedAt: timestamp("approved_at"),
  approvedBy: uuid("approved_by").references(() => users.id),
})

export const matchesRelations = relations(matches, ({ one }) => ({
  student: one(users, {
    fields: [matches.studentId],
    references: [users.id],
  }),
  organization: one(users, {
    fields: [matches.organizationId],
    references: [users.id],
  }),
  // Project relation for faculty matches (co-supervision support)
  project: one(projects, {
    fields: [matches.projectId],
    references: [projects.id],
  }),
  // Legacy faculty relation
  faculty: one(users, {
    fields: [matches.facultyId],
    references: [users.id],
  }),
}))

// Areas of Law relations
export const areasOfLawRelations = relations(areasOfLaw, ({ many }) => ({
  studentStatements: many(studentStatements),
  facultyProfiles: many(facultyProfiles),
  organizationProfiles: many(organizationProfiles),
}))

// Geographic Locations relations
export const geographicLocationsRelations = relations(
  geographicLocations,
  ({ many }) => ({
    organizationProfiles: many(organizationProfiles),
  })
)

// Email Templates relations (no foreign key relations)
export const emailTemplatesRelations = relations(emailTemplates, () => ({}))

// Student Grades relations
export const studentGradesRelations = relations(studentGrades, ({ one }) => ({
  student: one(users, {
    fields: [studentGrades.studentId],
    references: [users.id],
  }),
}))

// Student Statements relations
export const studentStatementsRelations = relations(
  studentStatements,
  ({ one }) => ({
    student: one(users, {
      fields: [studentStatements.studentId],
      references: [users.id],
    }),
    areaOfLaw: one(areasOfLaw, {
      fields: [studentStatements.areaOfLawId],
      references: [areasOfLaw.id],
    }),
  })
)

// Student Applications relations
export const studentApplicationsRelations = relations(
  studentApplications,
  ({ one }) => ({
    student: one(users, {
      fields: [studentApplications.studentId],
      references: [users.id],
    }),
    cvFile: one(files, {
      fields: [studentApplications.cvFileId],
      references: [files.id],
    }),
    coverLetterFile: one(files, {
      fields: [studentApplications.coverLetterFileId],
      references: [files.id],
    }),
    transcriptFile: one(files, {
      fields: [studentApplications.transcriptFileId],
      references: [files.id],
    }),
  })
)

// Student Area of Law Rankings relations
export const studentAreaOfLawRankingsRelations = relations(
  studentAreaOfLawRankings,
  ({ one }) => ({
    student: one(users, {
      fields: [studentAreaOfLawRankings.studentId],
      references: [users.id],
    }),
    areaOfLaw: one(areasOfLaw, {
      fields: [studentAreaOfLawRankings.areaOfLawId],
      references: [areasOfLaw.id],
    }),
  })
)

// Student Research Interests relations
export const studentResearchInterestsRelations = relations(
  studentResearchInterests,
  ({ one }) => ({
    student: one(users, {
      fields: [studentResearchInterests.studentId],
      references: [users.id],
    }),
    faculty: one(users, {
      fields: [studentResearchInterests.facultyId],
      references: [users.id],
    }),
  })
)

// Student Self-Proposed Placements relations
export const studentSelfProposedPlacementsRelations = relations(
  studentSelfProposedPlacements,
  ({ one }) => ({
    student: one(users, {
      fields: [studentSelfProposedPlacements.studentId],
      references: [users.id],
    }),
    reviewer: one(users, {
      fields: [studentSelfProposedPlacements.reviewedBy],
      references: [users.id],
    }),
  })
)

// Student Application Drafts relations
export const studentApplicationDraftsRelations = relations(
  studentApplicationDrafts,
  ({ one }) => ({
    user: one(users, {
      fields: [studentApplicationDrafts.userId],
      references: [users.id],
    }),
  })
)

// Training Modules relations
export const trainingModulesRelations = relations(
  trainingModules,
  ({ one, many }) => ({
    createdBy: one(users, {
      fields: [trainingModules.createdBy],
      references: [users.id],
    }),
    userProgress: many(userTrainingProgress),
  })
)

// User Training Progress relations
export const userTrainingProgressRelations = relations(
  userTrainingProgress,
  ({ one }) => ({
    user: one(users, {
      fields: [userTrainingProgress.userId],
      references: [users.id],
    }),
    trainingModule: one(trainingModules, {
      fields: [userTrainingProgress.trainingModuleId],
      references: [trainingModules.id],
    }),
  })
)

// Export all tables for easy access
export * from "drizzle-orm"

// Export audit logs
export * from "./audit-logs"

// Export notification preferences
export * from "./notification-preferences"
